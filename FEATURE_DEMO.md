# 断面统计详情功能演示

## 功能概述

本功能实现了断面统计详情页面，包括：
1. 断面潮流曲线图表
2. 断面基本信息展示
3. 统计数据表格
4. 时间范围查询
5. 数据导出功能

## 使用流程

### 1. 进入详情页面
- 在断面统计列表页面点击"查看"按钮
- 系统自动跳转到详情页面并加载数据

### 2. 查看断面信息
详情页面顶部显示断面基本信息：
- 断面名称：如"秦胜2M39线,双胜2Y17线,双胜2Y18线三线"
- 电压等级：如"220KV"
- 统计时段：如"2025-06-01 00:00:00 - 2025-06-30 23:59:59"
- 最大越线潮流：如"568.22"
- 越线最大值：如"88.22"
- 总越限时间：如"2565"
- 最长出现越限时长：如"255"

### 3. 查看潮流曲线
中间区域显示断面潮流曲线图表：
- **潮流值**：蓝色渐变区域图，显示实际潮流变化
- **限额值**：黄色直线，显示限额标准线
- X轴：时间轴
- Y轴：潮流值

### 4. 查看统计数据表格
底部表格显示详细统计数据，包含以下列：
- **日期**：统计日期
- **越限时间段**：越限发生的时间段
- **限额**：当日限额值（支持排序）
- **最大潮流**：当日最大潮流值（支持排序）
- **差额最大值**：最大差额值（支持排序）
- **总越限时间**：当日总越限时间（支持排序）
- **最长出现越限时长**：最长连续越限时长（支持排序）

### 5. 时间范围查询
- 页面顶部提供时间范围选择器
- 默认显示当前月份数据
- 可以自定义选择开始和结束时间
- 点击"搜索"按钮重新加载数据

### 6. 数据导出
- 点击"导出表格"按钮可导出当前数据
- 支持Excel格式导出

### 7. 返回列表
- 点击"返回"按钮回到断面统计列表页面

## 数据格式示例

### API请求参数
```json
{
  "startTime": "2025-06-01T00:00:00",
  "endTime": "2025-06-30T23:59:59",
  "sectionId": "10307332000000000243"
}
```

### API响应数据
```json
{
  "code": "0000",
  "data": {
    "sectionId": "10307332000000000243",
    "sectionName": "秦胜2M39线,双胜2Y17线,双胜2Y18线三线",
    "volt": "220KV",
    "period": "2025-06-01 00:00:00 - 2025-06-30 23:59:59",
    "maxValue": "568.22",
    "maxDiffValue": "88.22",
    "totalOverTime": "2565",
    "longestOverTime": "255",
    "dataList": [
      {
        "limit": "480",
        "value": "99.22",
        "diffValue": "-380.78",
        "time": "2025-06-01 00:40:00"
      }
    ],
    "statisticDataList": [
      {
        "limit": "480",
        "maxValue": "118.98",
        "maxDiffValue": "0.00",
        "totalOverTime": "-",
        "longestOverTime": "-",
        "time": "2025-06-01",
        "overPeriod": ""
      }
    ]
  }
}
```

## 技术特性

### 响应式设计
- 图表自适应容器大小
- 表格支持滚动和固定高度
- 移动端友好的交互设计

### 性能优化
- 使用computed计算属性优化数据处理
- 图表数据懒加载
- 表格虚拟滚动支持大数据量

### 用户体验
- 加载状态提示
- 错误处理和提示
- 数据为空时的友好提示
- 平滑的页面切换动画

### 数据安全
- 参数验证
- 错误边界处理
- 数据格式校验

## 扩展功能

### 可配置项
- 图表颜色主题
- 表格列显示/隐藏
- 数据刷新间隔
- 导出格式选择

### 未来增强
- 实时数据推送
- 数据对比功能
- 历史数据回放
- 预警阈值设置
