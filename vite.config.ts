import { fileURLToPath, URL } from 'node:url'
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueDevTools from 'vite-plugin-vue-devtools'
import UnoCSS from 'unocss/vite'
import svgLoader from 'vite-svg-loader'

// https://vite.dev/config/
export default defineConfig({
  base: './',
  plugins: [vue(), UnoCSS(), svgLoader(), vueDevTools()],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
    },
  },
  server: {
    proxy: {
      '/dwyzt': {
        target: 'http://172.19.139.41:8087/mock/189',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/dwyzt/, '/dwyzt'),
      },
    },
  },
})
