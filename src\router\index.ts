import { createRouter, createWebHashHistory } from 'vue-router'
import HomeView from '../views/main-view/index.vue'

const router = createRouter({
  history: createWebHashHistory(),
  routes: [
    {
      path: '/',
      name: 'home',
      component: HomeView,
      redirect: '/section-monitoring',
      children: [
        {
          path: 'section-monitoring',
          name: 'section-monitoring',
          component: () => import('../views/section-monitoring/index.vue'),
        },
      ],
    },
  ],
})

export default router
