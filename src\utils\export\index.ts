/**
 * 导出工具类
 * 支持Excel、CSV、JSON等格式的数据导出
 */
import * as XLSX from 'xlsx'

export interface ExportColumn {
  key: string
  title: string
  width?: number
  formatter?: (value: any) => string
}

export interface ExportOptions {
  filename?: string
  sheetName?: string
  columns?: ExportColumn[]
  data: any[]
  exportType?: 'excel' | 'csv' | 'json'
}

/**
 * 导出为CSV格式
 */
export const exportToCSV = (options: ExportOptions) => {
  const { data, columns, filename = 'export_data' } = options

  if (!data || data.length === 0) {
    throw new Error('没有数据可导出')
  }

  // 如果没有指定列，则使用数据的所有键
  const exportColumns =
    columns ||
    Object.keys(data[0]).map((key) => ({
      key,
      title: key,
    }))

  // 生成CSV内容
  const headers = exportColumns.map((col) => col.title).join(',')
  const rows = data.map((row) =>
    exportColumns
      .map((col) => {
        let value = row[col.key] || ''

        // 如果有格式化函数，使用格式化函数
        // if (col.formatter) {
        //   value = col.formatter(value)
        // }

        // 处理包含逗号、换行符或双引号的值
        if (
          typeof value === 'string' &&
          (value.includes(',') || value.includes('\n') || value.includes('"'))
        ) {
          value = `"${value.replace(/"/g, '""')}"`
        }

        return value
      })
      .join(','),
  )

  const csvContent = [headers, ...rows].join('\n')

  // 添加BOM以支持中文
  const BOM = '\uFEFF'
  downloadFile(BOM + csvContent, `${filename}.csv`, 'text/csv;charset=utf-8')
}

/**
 * 导出为JSON格式
 */
export const exportToJSON = (options: ExportOptions) => {
  const { data, filename = 'export_data' } = options

  if (!data || data.length === 0) {
    throw new Error('没有数据可导出')
  }

  const jsonContent = JSON.stringify(data, null, 2)
  downloadFile(jsonContent, `${filename}.json`, 'application/json')
}

/**
 * 导出为Excel格式,使用 xlsx 库
 * 表头使用columns
 */
export const exportToExcel = (options: ExportOptions) => {
  const { data, columns, filename = 'export_data' } = options

  if (!data || data.length === 0) {
    throw new Error('没有数据可导出')
  }

  // 把 data 转换成按 columns 中的 key 映射的数组
  const formattedData = data.map((row) => {
    const newRow: Record<string, any> = {}
    columns?.forEach((col) => {
      newRow[col.title] = row[col.key]
    })
    return newRow
  })

  const workbook = XLSX.utils.book_new()
  const worksheet = XLSX.utils.json_to_sheet(formattedData, {
    header: columns?.map((col) => col.title),
  })
  XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1')
  XLSX.writeFile(workbook, `${filename}.xlsx`)
}

/**
 * 下载文件
 */
const downloadFile = (content: string, filename: string, mimeType: string) => {
  const blob = new Blob([content], { type: mimeType })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = filename
  link.style.display = 'none'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(url)
}

/**
 * 通用导出函数
 */
export const exportData = (options: ExportOptions) => {
  const { exportType = 'excel' } = options

  switch (exportType) {
    case 'csv':
      return exportToCSV(options)
    case 'json':
      return exportToJSON(options)
    case 'excel':
    default:
      return exportToExcel(options)
  }
}

/**
 * 格式化常用数据类型
 */
export const formatters = {
  // 日期格式化
  date: (value: any) => {
    if (!value) return ''
    const date = new Date(value)
    return date.toLocaleDateString('zh-CN')
  },

  // 日期时间格式化
  datetime: (value: any) => {
    if (!value) return ''
    const date = new Date(value)
    return date.toLocaleString('zh-CN')
  },

  // 数字格式化（保留两位小数）
  number: (value: any) => {
    if (value === null || value === undefined || value === '') return ''
    const num = parseFloat(value)
    return isNaN(num) ? value : num.toFixed(2)
  },

  // 货币格式化
  currency: (value: any) => {
    if (value === null || value === undefined || value === '') return ''
    const num = parseFloat(value)
    return isNaN(num) ? value : `¥${num.toFixed(2)}`
  },
}
