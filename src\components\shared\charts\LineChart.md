# LineChart 组件使用文档

## 概述

LineChart 是一个基于 ECharts 的 Vue 3 曲线图组件，支持渐变色填充、多系列数据展示、自定义配置等功能。

## 特性

- ✅ 基于 ECharts 5.6.0
- ✅ 支持渐变色区域填充
- ✅ 图表标题在左侧显示
- ✅ 多系列数据支持
- ✅ 响应式设计
- ✅ TypeScript 支持
- ✅ 可配置的样式选项

## 基础用法

```vue
<template>
  <LineChart
    title="销售数据"
    :data="chartData"
    height="400px"
  />
</template>

<script setup lang="ts">
import LineChart from '@/components/shared/LineChart.vue'

const chartData = [
  {
    name: '销售额',
    data: [
      { name: '1月', value: 120 },
      { name: '2月', value: 132 },
      { name: '3月', value: 101 },
      { name: '4月', value: 134 },
      { name: '5月', value: 90 },
      { name: '6月', value: 230 },
    ],
  },
]
</script>
```

## Props 配置

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| title | string | '' | 图表标题，显示在左侧 |
| data | SeriesData[] | [] | 图表数据 |
| width | string | '100%' | 图表宽度 |
| height | string | '400px' | 图表高度 |
| xAxisData | string[] | undefined | 自定义 X 轴数据 |
| showGrid | boolean | true | 是否显示网格 |
| showLegend | boolean | true | 是否显示图例 |
| smooth | boolean | true | 是否平滑曲线 |
| areaStyle | boolean | true | 是否显示区域填充 |

## 数据格式

### SeriesData 接口

```typescript
interface SeriesData {
  name: string                    // 系列名称
  data: DataPoint[]              // 数据点数组
  color?: string                 // 自定义线条颜色
  gradientColors?: {             // 自定义渐变色
    start: string
    end: string
  }
}
```

### DataPoint 接口

```typescript
interface DataPoint {
  name: string                   // 数据点名称（X轴标签）
  value: number                  // 数据点值（Y轴值）
}
```

## 默认渐变色

组件内置了多种渐变色配置：

1. 蓝色：`rgba(58,77,233,0.8)` → `rgba(58,77,233,0.3)`
2. 红色：`rgba(213,72,120,0.8)` → `rgba(213,72,120,0.3)`
3. 绿色：`rgba(46,204,113,0.8)` → `rgba(46,204,113,0.3)`
4. 黄色：`rgba(241,196,15,0.8)` → `rgba(241,196,15,0.3)`

## 高级用法

### 多系列数据

```vue
<template>
  <LineChart
    title="对比数据"
    :data="multiSeriesData"
    height="350px"
  />
</template>

<script setup lang="ts">
const multiSeriesData = [
  {
    name: '本年',
    data: [
      { name: '1月', value: 120 },
      { name: '2月', value: 132 },
      // ...
    ],
  },
  {
    name: '去年',
    data: [
      { name: '1月', value: 100 },
      { name: '2月', value: 110 },
      // ...
    ],
  },
]
</script>
```

### 自定义渐变色

```vue
<template>
  <LineChart
    title="自定义颜色"
    :data="customColorData"
  />
</template>

<script setup lang="ts">
const customColorData = [
  {
    name: '自定义系列',
    data: [
      { name: 'A', value: 50 },
      { name: 'B', value: 80 },
      // ...
    ],
    gradientColors: {
      start: 'rgba(255, 99, 132, 0.8)',
      end: 'rgba(255, 99, 132, 0.2)',
    },
  },
]
</script>
```

### 禁用区域填充

```vue
<template>
  <LineChart
    title="线条图"
    :data="lineData"
    :area-style="false"
  />
</template>
```

## 方法

组件通过 `defineExpose` 暴露了以下方法：

- `chartInstance`: ECharts 实例
- `updateChart()`: 手动更新图表

```vue
<template>
  <LineChart ref="chartRef" :data="data" />
</template>

<script setup lang="ts">
import { ref } from 'vue'

const chartRef = ref()

// 手动更新图表
const updateChart = () => {
  chartRef.value?.updateChart()
}

// 获取 ECharts 实例
const getChartInstance = () => {
  return chartRef.value?.chartInstance
}
</script>
```

## 注意事项

1. 确保项目已安装 ECharts 依赖
2. 数据变化时组件会自动重新渲染
3. 组件会自动处理窗口大小变化
4. 标题采用垂直排列显示在图表左侧
