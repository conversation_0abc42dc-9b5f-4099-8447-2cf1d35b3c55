# 共享组件使用文档

## SearchForm 组件

搜索表单组件，提供统一的搜索和重置功能。

### 基础用法

```vue
<template>
  <SearchForm :loading="loading" @search="handleSearch" @reset="handleReset">
    <template #form-fields>
      <!-- 在这里放置表单字段 -->
      <InputGroup size="large" label="名称" class="mr-2.5">
        <n-input v-model:value="formData.name" placeholder="请输入名称" clearable />
      </InputGroup>
      
      <InputGroup size="large" label="状态" class="mr-2.5">
        <n-select v-model:value="formData.status" :options="statusOptions" clearable />
      </InputGroup>
    </template>
    
    <template #action-buttons>
      <!-- 在这里放置额外的操作按钮 -->
      <ExportButton :data="tableData" :columns="columns" />
    </template>
  </SearchForm>
</template>

<script setup lang="ts">
import SearchForm from '@/components/shared/SearchForm.vue'
import ExportButton from '@/components/shared/ExportButton.vue'

const loading = ref(false)
const formData = ref({
  name: '',
  status: null,
})

const handleSearch = () => {
  // 执行搜索逻辑
  console.log('搜索:', formData.value)
}

const handleReset = () => {
  // 重置表单数据
  formData.value = {
    name: '',
    status: null,
  }
  // 重新获取数据
  fetchData()
}
</script>
```

### Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| loading | boolean | false | 是否显示加载状态 |

### Events

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| search | 点击搜索按钮时触发 | - |
| reset | 点击重置按钮时触发 | - |

### Slots

| 插槽名 | 说明 |
|--------|------|
| form-fields | 表单字段区域 |
| action-buttons | 额外的操作按钮区域 |

## ExportButton 组件

导出按钮组件，支持Excel、CSV、JSON格式的数据导出。

### 基础用法

```vue
<template>
  <ExportButton 
    :data="tableData" 
    :columns="columns" 
    filename="export_data"
    export-type="excel"
    @export="handleExport"
  />
</template>

<script setup lang="ts">
import ExportButton from '@/components/shared/ExportButton.vue'

const tableData = ref([
  { id: 1, name: '张三', age: 25 },
  { id: 2, name: '李四', age: 30 },
])

const columns = ref([
  { key: 'id', title: 'ID' },
  { key: 'name', title: '姓名' },
  { key: 'age', title: '年龄' },
])

const handleExport = (data, columns, filename, exportType) => {
  console.log('导出数据:', { data, columns, filename, exportType })
}
</script>
```

### Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| text | string | '导出表格' | 按钮文本 |
| size | string | 'large' | 按钮大小 |
| buttonClass | string | '' | 按钮样式类 |
| loading | boolean | false | 是否显示加载状态 |
| disabled | boolean | false | 是否禁用 |
| data | array | [] | 要导出的数据 |
| columns | array | [] | 列配置 |
| filename | string | 'export_data' | 导出文件名 |
| exportType | string | 'excel' | 导出类型：excel/csv/json |

### Events

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| export | 导出时触发 | (data, columns, filename, exportType) |
| export-start | 开始导出时触发 | - |
| export-success | 导出成功时触发 | (filename) |
| export-error | 导出失败时触发 | (error) |

## 导出工具类

位于 `src/utils/export/index.ts`，提供通用的数据导出功能。

### 基础用法

```typescript
import { exportData, formatters } from '@/utils/export'

// 导出Excel
exportData({
  data: tableData,
  columns: [
    { key: 'id', title: 'ID' },
    { key: 'name', title: '姓名' },
    { key: 'createTime', title: '创建时间', formatter: formatters.datetime },
    { key: 'amount', title: '金额', formatter: formatters.currency },
  ],
  filename: 'user_data',
  exportType: 'excel',
})
```

### 支持的格式化器

- `formatters.date`: 日期格式化
- `formatters.datetime`: 日期时间格式化
- `formatters.number`: 数字格式化（保留两位小数）
- `formatters.currency`: 货币格式化

## 特性

1. **统一的UI风格**: 所有组件都遵循项目的设计规范
2. **TypeScript支持**: 完整的类型定义和类型检查
3. **灵活的配置**: 支持多种配置选项和自定义
4. **事件驱动**: 通过事件进行组件间通信
5. **插槽支持**: 提供灵活的内容定制能力
6. **国际化友好**: 支持中文排序和格式化
