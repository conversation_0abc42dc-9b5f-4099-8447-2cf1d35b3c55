/**
 * 排序工具函数
 */

/**
 * 断面统计数据类型
 */
export interface SectionStatisticData {
  id?: string
  name?: string
  volt?: string
  limit: string
  maxValue: string
  maxDiffValue: string
  totalOverTime: string
  longestOverTime: string
  maxValueTime?: string
  time?: string
  overPeriod?: string
}

/**
 * 电压等级排序权重
 */
const VOLT_ORDER: { [key: string]: number } = {
  '500KV': 3,
  '220KV': 2,
  '110KV': 1,
}

/**
 * 默认排序函数 - 按照 电压等级>总越限时间>差额最大值>最大潮流>名字首字母 的优先级排序
 * @param data 要排序的数据数组
 * @returns 排序后的数据数组
 */
export const applyDefaultSort = <T extends SectionStatisticData>(data: T[]): T[] => {
  return [...data].sort((a, b) => {
    // 1. 电压等级排序（最高优先级）
    if (a.volt && b.volt) {
      const aVolt = VOLT_ORDER[a.volt] || 0
      const bVolt = VOLT_ORDER[b.volt] || 0
      if (aVolt !== bVolt) {
        return bVolt - aVolt // 降序，高电压优先
      }
    }

    // 2. 总越限时间排序
    if (a.totalOverTime && b.totalOverTime) {
      const aTotalOverTime = parseFloat(a.totalOverTime) || 0
      const bTotalOverTime = parseFloat(b.totalOverTime) || 0
      if (aTotalOverTime !== bTotalOverTime) {
        return bTotalOverTime - aTotalOverTime // 降序
      }
    }

    // 3. 差额最大值排序
    if (a.maxDiffValue && b.maxDiffValue) {
      const aMaxDiffValue = parseFloat(a.maxDiffValue) || 0
      const bMaxDiffValue = parseFloat(b.maxDiffValue) || 0
      if (aMaxDiffValue !== bMaxDiffValue) {
        return bMaxDiffValue - aMaxDiffValue // 降序
      }
    }

    // 4. 最大潮流排序
    if (a.maxValue && b.maxValue) {
      const aMaxValue = parseFloat(a.maxValue) || 0
      const bMaxValue = parseFloat(b.maxValue) || 0
      if (aMaxValue !== bMaxValue) {
        return bMaxValue - aMaxValue // 降序
      }
    }

    // 5. 名字首字母排序（最低优先级）
    const aName = String(a.name || '')
    const bName = String(b.name || '')
    if (aName && bName) {
      return aName.localeCompare(bName, 'zh-CN', { numeric: true }) // 升序
    }

    // 6. 时间排序（详情页面特有）
    if (a.time && b.time) {
      return new Date(a.time).getTime() - new Date(b.time).getTime() // 升序
    }

    return 0
  })
}

/**
 * 电压等级比较函数
 * @param a 第一个数据项
 * @param b 第二个数据项
 * @returns 比较结果
 */
export const compareVoltage = (a: any, b: any): number => {
  const aVolt = VOLT_ORDER[a.volt] || 0
  const bVolt = VOLT_ORDER[b.volt] || 0
  return bVolt - aVolt // 降序，高电压优先
}

/**
 * 数值字段比较函数（降序）
 * @param fieldName 字段名
 * @returns 比较函数
 */
export const compareNumericDesc = (fieldName: string) => {
  return (a: any, b: any): number => {
    const aNum = parseFloat(a[fieldName]) || 0
    const bNum = parseFloat(b[fieldName]) || 0
    return bNum - aNum // 降序
  }
}

/**
 * 数值字段比较函数（升序）
 * @param fieldName 字段名
 * @returns 比较函数
 */
export const compareNumericAsc = (fieldName: string) => {
  return (a: any, b: any): number => {
    const aNum = parseFloat(a[fieldName]) || 0
    const bNum = parseFloat(b[fieldName]) || 0
    return aNum - bNum // 升序
  }
}

/**
 * 字符串字段比较函数
 * @param fieldName 字段名
 * @returns 比较函数
 */
export const compareString = (fieldName: string) => {
  return (a: any, b: any): number => {
    const aStr = String(a[fieldName] || '')
    const bStr = String(b[fieldName] || '')
    return aStr.localeCompare(bStr, 'zh-CN', { numeric: true })
  }
}

/**
 * 时间字段比较函数
 * @param fieldName 字段名
 * @returns 比较函数
 */
export const compareTime = (fieldName: string) => {
  return (a: any, b: any): number => {
    const aTime = new Date(a[fieldName]).getTime()
    const bTime = new Date(b[fieldName]).getTime()
    return aTime - bTime // 升序
  }
}

/**
 * 多列排序处理函数
 * @param data 要排序的数据
 * @param multiSortState 多列排序状态
 * @param columns 列配置
 * @returns 排序后的数据
 */
export const handleMultiSort = (data: any[], multiSortState: any, columns: any[]): any[] => {
  if (Object.keys(multiSortState).length === 0) {
    return data
  }

  // 按优先级排序多列排序状态
  const sortColumns = Object.entries(multiSortState)
    .map(([key, state]: [string, any]) => ({
      key,
      order: state.order,
      multiple: state.multiple,
      column: columns.find((col: any) => col.key === key),
    }))
    .sort((a, b) => b.multiple - a.multiple) // 按优先级降序排列

  return [...data].sort((a, b) => {
    for (const sortCol of sortColumns) {
      let result = 0

      // 如果列有自定义比较函数，使用它
      if (
        sortCol.column?.sorter &&
        typeof sortCol.column.sorter === 'object' &&
        sortCol.column.sorter.compare
      ) {
        result = sortCol.column.sorter.compare(a, b)
      } else {
        // 默认排序逻辑
        const aValue = (a as any)[sortCol.key]
        const bValue = (b as any)[sortCol.key]

        // 处理数值类型的列
        if (
          ['limit', 'maxValue', 'maxDiffValue', 'totalOverTime', 'longestOverTime'].includes(
            sortCol.key,
          )
        ) {
          const aNum = parseFloat(aValue) || 0
          const bNum = parseFloat(bValue) || 0
          result = aNum - bNum
        } else {
          // 处理字符串类型的列
          const aStr = String(aValue || '')
          const bStr = String(bValue || '')
          result = aStr.localeCompare(bStr, 'zh-CN', { numeric: true })
        }
      }

      // 根据排序方向调整结果
      if (sortCol.order === 'desc') {
        result = -result
      }

      // 如果当前列的比较结果不为0，返回结果
      if (result !== 0) {
        return result
      }
    }

    return 0 // 所有列都相等
  })
}
