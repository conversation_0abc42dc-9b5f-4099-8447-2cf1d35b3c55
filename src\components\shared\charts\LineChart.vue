<template>
  <div>
    <div ref="chartRef" :style="{ width: width, height: height }"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import * as echarts from 'echarts'
import type { LineChartProps, GradientColors } from '@/types/chart'
import { DEFAULT_GRADIENT_COLORS, DEFAULT_CHART_THEME } from '@/types/chart'

const props = withDefaults(defineProps<LineChartProps>(), {
  title: '',
  width: '100%',
  height: '400px',
  showGrid: true,
  showLegend: true,
  smooth: true,
  areaStyle: true,
})

const chartRef = ref<HTMLDivElement>()
let chartInstance: echarts.ECharts | null = null

// 创建渐变色
const createGradientColor = (colors: GradientColors) => {
  return new echarts.graphic.LinearGradient(0, 0, 0, 1, [
    {
      offset: 0,
      color: colors.start,
    },
    {
      offset: 1,
      color: colors.end,
    },
  ])
}

// 获取图表配置
const getChartOption = () => {
  const series = props.data.map((item, index) => {
    const gradientColors =
      item.gradientColors || DEFAULT_GRADIENT_COLORS[index % DEFAULT_GRADIENT_COLORS.length]

    return {
      name: item.name,
      type: 'line',
      smooth: props.smooth,
      data: item.data.map((point) => point.value),
      lineStyle: {
        width: 2,
        color: item.color || gradientColors.start.replace(/,0\.\d+\)/, ',1)'),
      },
      areaStyle: props.areaStyle
        ? {
            color: createGradientColor(gradientColors),
          }
        : undefined,
      symbol: 'none',
      itemStyle: {
        color: item.color || gradientColors.start.replace(/,0\.\d+\)/, ',1)'),
      },
    }
  })

  // 获取 X 轴数据
  const xAxisData = props.xAxisData || props.data[0]?.data.map((point) => point.name) || []

  return {
    title: {
      text: props.title,
      left: 20,
      textStyle: {
        color: DEFAULT_CHART_THEME.textColor,
        fontSize: 20,
        fontWeight: 'bold',
      },
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: DEFAULT_CHART_THEME.gridColor,
      borderWidth: 1,
      textStyle: {
        color: DEFAULT_CHART_THEME.textColor,
      },
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#999',
        },
      },
    },
    legend: {
      show: props.showLegend,
      icon: 'rect',
      itemHeight: 2,
      top: 10,
      right: 20,
      textStyle: {
        color: DEFAULT_CHART_THEME.textColor,
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: props.showLegend ? '15%' : '5%',
      containLabel: true,
      show: props.showGrid,
      borderColor: DEFAULT_CHART_THEME.gridColor,
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: xAxisData,
      axisLine: {
        lineStyle: {
          color: DEFAULT_CHART_THEME.axisLineColor,
        },
      },
      axisLabel: {
        color: '#909399',
      },
      splitLine: {
        show: props.showGrid,
        lineStyle: {
          color: DEFAULT_CHART_THEME.splitLineColor,
        },
      },
    },
    yAxis: {
      type: 'value',
      axisLine: {
        lineStyle: {
          color: DEFAULT_CHART_THEME.axisLineColor,
        },
      },
      axisLabel: {
        color: '#909399',
      },
      splitLine: {
        show: props.showGrid,
        lineStyle: {
          color: DEFAULT_CHART_THEME.splitLineColor,
        },
      },
    },
    series,
  }
}

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return

  chartInstance = echarts.init(chartRef.value)
  chartInstance.setOption(getChartOption())
}

// 更新图表
const updateChart = () => {
  if (chartInstance) {
    chartInstance.setOption(getChartOption(), true)
  }
}

// 监听数据变化
watch(
  () => props.data,
  () => {
    updateChart()
  },
  { deep: true },
)

// 监听其他配置变化
watch(
  [() => props.showGrid, () => props.showLegend, () => props.smooth, () => props.areaStyle],
  () => {
    updateChart()
  },
)

// 处理窗口大小变化
const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

onMounted(async () => {
  await nextTick()
  initChart()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
  window.removeEventListener('resize', handleResize)
})

// 暴露图表实例给父组件
defineExpose({
  chartInstance,
  updateChart,
})
</script>

<style scoped></style>
