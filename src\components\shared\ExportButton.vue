<template>
  <n-button
    :size="size"
    :class="buttonClass"
    @click="handleExport"
    :loading="loading"
    :disabled="disabled"
  >
    <template #icon>
      <n-icon><ExportIcon /></n-icon>
    </template>
    {{ text }}
  </n-button>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { NButton, NIcon, useMessage } from 'naive-ui'
import { ExportIcon } from '@/utils/constant/icons'
import { exportData, type ExportColumn } from '@/utils/export'

// 定义 props
interface Props {
  text?: string
  size?: 'small' | 'medium' | 'large'
  buttonClass?: string
  loading?: boolean
  disabled?: boolean
  data?: any[]
  columns?: any[]
  filename?: string
  exportType?: 'excel' | 'csv' | 'json'
}

const props = withDefaults(defineProps<Props>(), {
  text: '导出表格',
  size: 'large',
  buttonClass: '',
  loading: false,
  disabled: false,
  data: () => [],
  columns: () => [],
  filename: 'export_data',
  exportType: 'excel',
})

// 定义 emits
const emit = defineEmits<{
  export: [data: any[], columns: any[], filename: string, exportType: string]
  'export-start': []
  'export-success': [filename: string]
  'export-error': [error: Error]
}>()

const message = useMessage()

// 导出处理函数
const handleExport = async () => {
  try {
    emit('export-start')

    // 检查数据
    if (!props.data || props.data.length === 0) {
      message.warning('暂无数据可导出')
      return
    }

    // 触发导出事件
    emit('export', props.data, props.columns, props.filename, props.exportType)

    // 这里可以添加具体的导出逻辑
    await performExport()

    emit('export-success', props.filename)
    message.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    emit('export-error', error as Error)
    message.error('导出失败，请重试')
  }
}

// 执行导出逻辑
const performExport = async () => {
  // 转换列配置格式
  const exportColumns: ExportColumn[] = props.columns.map((col) => ({
    key: col.key,
    title: col.title,
    width: col.width ? parseInt(col.width) : undefined,
  }))

  // 使用导出工具进行导出
  exportData({
    data: props.data,
    columns: exportColumns,
    filename: props.filename,
    exportType: props.exportType,
  })
}
</script>

<style scoped></style>
