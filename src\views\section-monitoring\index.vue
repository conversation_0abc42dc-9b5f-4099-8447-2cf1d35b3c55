<template>
  <component :is="!!sectionMonitoringStore.selectedTableRow ? TableInfo : TableView"></component>
</template>

<script setup lang="ts">
import { useSectionMonitoringStore } from '@/stores/'

import TableView from '@/components/features/section-monitoring/overview/SectionStatistic.vue'
import TableInfo from '@/components/features/section-monitoring/info/SectionInfo.vue'

const sectionMonitoringStore = useSectionMonitoringStore()
</script>
