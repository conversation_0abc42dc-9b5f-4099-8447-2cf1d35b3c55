# 断面统计详情功能实现总结

## 实现内容

### 1. API接口完善

#### 新增API端点配置
- 在 `src/utils/api/config.ts` 中添加了 `SECTION_STATISTIC_DETAIL: '/getSectionStatisticDetail'`

#### 新增数据类型定义
在 `src/utils/api/services/sectionMonitoring.ts` 中添加了：

```typescript
// 断面统计详情查询参数
export interface SectionStatisticDetailParams {
  startTime: string
  endTime: string
  sectionId: string
}

// 断面统计详情数据点
export interface SectionStatisticDetailDataPoint {
  limit: string
  value: string
  diffValue: string
  time: string
}

// 断面统计详情统计数据点
export interface SectionStatisticDetailStatisticDataPoint {
  limit: string
  maxValue: string
  maxDiffValue: string
  totalOverTime: string
  longestOverTime: string
  time: string
  overPeriod: string
}

// 断面统计详情响应数据
export interface SectionStatisticDetailResponse {
  sectionId: string
  sectionName: string
  volt: string
  period: string
  maxValue: string
  maxDiffValue: string
  totalOverTime: string
  longestOverTime: string
  dataList: SectionStatisticDetailDataPoint[]
  statisticDataList: SectionStatisticDetailStatisticDataPoint[]
}
```

#### 新增API服务方法
```typescript
static async getSectionStatisticDetail(
  params: SectionStatisticDetailParams,
): Promise<SectionStatisticDetailResponse> {
  return api.get<SectionStatisticDetailResponse>(
    API_ENDPOINTS.SPOT_MARKET.SECTION_STATISTIC_DETAIL,
    params,
  )
}
```

### 2. 页面功能完善

#### info/index.vue 页面更新
- 添加了时间选择器的双向绑定
- 实现了数据获取逻辑 `fetchSectionStatisticDetail`
- 更新了图表数据格式，支持动态数据
- 更新了表格列配置，符合需求规格
- 添加了搜索和导出功能
- 实现了自动数据加载逻辑

#### SectionDetail.vue 组件更新
- 添加了 props 接收父组件传递的数据
- 实现了动态数据显示
- 支持数据为空时的默认显示

### 3. 数据流程

1. **参数来源**：
   - `startTime`, `endTime`: 来自 `info/index.vue` 中的 `n-date-picker` 组件
   - `sectionId`: 来自 `SectionStatistic.vue` 中表格查看按钮点击的 `item.id`

2. **数据处理**：
   - `dataList`: 用于 LineChart 组件显示断面潮流曲线（潮流值 + 限额值）
   - `statisticDataList`: 用于 DataTable 组件显示统计数据
   - 其他字段: 用于 SectionDetail 组件显示基本信息

3. **图表配置**：
   - 潮流值：蓝色渐变区域图
   - 限额值：黄色直线 `rgba(230, 176, 46, 1)`，无渐变填充

4. **表格配置**：
   - 列：日期、越限时间段、限额、最大潮流（排序）、差额最大值（排序）、总越限时间（排序）、最长出现越限时长（排序）

### 4. 功能特性

- ✅ 时间范围选择器
- ✅ 自动数据加载（选中行变化时）
- ✅ 手动搜索功能
- ✅ 加载状态显示
- ✅ 动态图表数据
- ✅ 动态表格数据
- ✅ 动态详情信息
- ✅ 导出功能接口（待实现具体逻辑）
- ✅ 返回功能

### 5. 使用示例

```typescript
// API调用示例
const response = await SectionMonitoringService.getSectionStatisticDetail({
  startTime: '2025-06-01T00:00:00',
  endTime: '2025-06-30T23:59:59',
  sectionId: '10307332000000000243'
})
```

## 注意事项

1. 所有的数值字段在前端都会进行 `parseFloat` 处理以确保图表正常显示
2. 时间格式统一使用 ISO 字符串格式
3. 表格支持排序功能，数值字段可以进行升序/降序排列
4. 图表会根据数据动态调整，无数据时显示空状态
5. 组件间通过 store 进行状态管理，确保数据一致性

## 待完善功能

1. 导出功能的具体实现
2. 错误处理和用户提示
3. 数据缓存机制
4. 性能优化
